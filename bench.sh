#!/usr/bin/env bash
# bench.sh — corre autocannon sobre todas las APIs y guarda resultados por endpoint + resumen CSV

BASE_URL="${BASE_URL:-http://localhost:3001}"
CONCURRENCY="${CONCURRENCY:-100}"
DURATION="${DURATION:-30}"       # segundos
OUTPUT_DIR="${OUTPUT_DIR:-bench-results}"
TS="$(date +%Y%m%d-%H%M%S)"

ENDPOINTS=(
  "/api/health"
  "/api/blockchain"
  "/api/blockchain/block"
  "/api/blockchain/mempool"
  "/api/blockchain/transactions"
  "/api/blockchain/predictions"
  "/api/blockchain/whales"
  "/api/blockchain/network"
  "/api/blockchain/analytics"
  "/api/all"
)

mkdir -p "$OUTPUT_DIR/$TS"
SUMMARY_CSV="$OUTPUT_DIR/$TS/summary.csv"

# Cabecera del CSV
echo "endpoint,req_per_sec_avg,latency_avg_ms,non2xx,responses_2xx,bytes_per_sec" > "$SUMMARY_CSV"

echo "Running benchmarks against $BASE_URL with c=$CONCURRENCY, d=${DURATION}s"
for ep in "${ENDPOINTS[@]}"; do
  url="${BASE_URL}${ep}"
  out_json="$OUTPUT_DIR/$TS$(echo "$ep" | sed 's|/|_|g').json"

  echo "→ $url"
  # --json: salida en JSON; --renderStatusCodes: incluye desglose por status
  npx autocannon "$url" -c "$CONCURRENCY" -d "$DURATION" --json --renderStatusCodes > "$out_json"

  # Extrae métricas clave con jq si está disponible
  if command -v jq >/dev/null 2>&1; then
    rps_avg=$(jq -r '.requests.average // 0' "$out_json")
    lat_avg=$(jq -r '.latency.average // 0' "$out_json")
    bytes_avg=$(jq -r '.throughput.average // 0' "$out_json")
    # non-2xx (suma de status != 2xx). Si no está, asumimos 0
    non2xx=$(jq -r '[.statusCodeStats|to_entries[]|select(.key|startswith("2")|not)|.value]|add // 0' "$out_json")
    res2xx=$(jq -r '.statusCodeStats["2xx"] // 0' "$out_json")
  else
    rps_avg=""
    lat_avg=""
    bytes_avg=""
    non2xx=""
    res2xx=""
  fi

  echo "${ep},${rps_avg},${lat_avg},${non2xx},${res2xx},${bytes_avg}" >> "$SUMMARY_CSV"
done

echo
echo "✅ Hecho. Resultados:"
echo "• JSON por endpoint: $OUTPUT_DIR/$TS/*.json"
echo "• Resumen CSV:       $SUMMARY_CSV"
echo
echo "Sugerencia:"
echo "  BASE_URL=http://localhost:3001 CONCURRENCY=100 DURATION=30 bash bench.sh"
