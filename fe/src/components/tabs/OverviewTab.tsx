import React from 'react';
import type { BlockData, MempoolData, Predictions } from '../EnhancedBlockchainTab';
import { formatNumber, formatDate, getMempoolStatus, getSentimentColor, getConfidenceLevel } from '../../utils/blockchainUtils';

interface OverviewTabProps {
  currentBlock: BlockData | null;
  mempoolData: MempoolData | null;
  predictions: Predictions | null;
}

export const OverviewTab: React.FC<OverviewTabProps> = ({
  currentBlock,
  mempoolData,
  predictions
}) => {
  return (
    <>
      {/* Current Block Card */}
      {currentBlock ? (
        <div className="price-card blockchain-card">
          <div className="card-header">
            <h3>🧱 Último Bloque de Bitcoin</h3>
            <div className="header-badges">
              <span className="source-badge">{currentBlock.source}</span>
              <span className="block-badge">#{formatNumber(currentBlock.height, 0)}</span>
            </div>
          </div>

          <div className="block-display">
            <div className="block-main-info">
              <div className="block-height-section">
                <div className="block-height">#{formatNumber(currentBlock.height, 0)}</div>
                <div className="block-subtitle">Altura del Bloque</div>
              </div>
              <div className="block-time-info">
                <div className="block-time">{formatDate(currentBlock.timestamp)}</div>
                <div className="time-ago">
                  Hace {Math.round((Date.now() - currentBlock.timestamp * 1000) / 60000)} minutos
                </div>
              </div>
            </div>

            <div className="block-stats-grid">
              <div className="block-stat">
                <div className="stat-icon">💸</div>
                <div className="stat-content">
                  <span className="stat-value">{formatNumber(currentBlock.txCount, 0)}</span>
                  <span className="stat-label">Transacciones</span>
                </div>
              </div>
              <div className="block-stat">
                <div className="stat-icon">💰</div>
                <div className="stat-content">
                  <span className="stat-value">{formatNumber(currentBlock.totalFees / 100000000, 4)} BTC</span>
                  <span className="stat-label">Total Fees</span>
                </div>
              </div>
              <div className="block-stat">
                <div className="stat-icon">📏</div>
                <div className="stat-content">
                  <span className="stat-value">{formatNumber(currentBlock.size / 1024, 0)} KB</span>
                  <span className="stat-label">Tamaño</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      ) : (
        <div className="loading-card">Cargando datos del último bloque...</div>
      )}

      {/* Enhanced Mempool with predictions */}
      <div className="grid-container">
        <div className="card">
          <div className="card-header">
            <h3>📊 Estado del Mempool</h3>
            {predictions?.fees && (
              <span className="prediction-badge">
                Predicción: {predictions.fees.nextHour} sat/vB próxima hora
              </span>
            )}
          </div>

          {mempoolData ? (
            <div className="mempool-enhanced">
              <div className="mempool-status-indicator">
                <div className={`status-circle ${getMempoolStatus(mempoolData.count).color}`}></div>
                <div className="status-info">
                  <div className="status-title">{getMempoolStatus(mempoolData.count).status}</div>
                  <div className="status-description">{getMempoolStatus(mempoolData.count).description}</div>
                </div>
              </div>

              <div className="mempool-stats">
                <div className="mempool-stat">
                  <div className="stat-icon">⏳</div>
                  <div className="stat-content">
                    <div className="stat-value">{formatNumber(mempoolData.count, 0)}</div>
                    <div className="stat-label">Transacciones Esperando</div>
                  </div>
                </div>
                <div className="mempool-stat">
                  <div className="stat-icon">💰</div>
                  <div className="stat-content">
                    <div className="stat-value">{formatNumber(mempoolData.totalFees / 100000000, 2)}</div>
                    <div className="stat-label">BTC en Fees</div>
                  </div>
                </div>
              </div>
            </div>
          ) : (
            <div className="no-data">Cargando datos del mempool...</div>
          )}
        </div>

        {/* Quick Sentiment Card */}
        <div className="card">
          <div className="card-header">
            <h3>🎯 Sentimiento de Red</h3>
          </div>

          {predictions?.sentiment ? (
            <div className="sentiment-display">
              <div className="sentiment-main" style={{ textAlign: 'center', padding: '1rem' }}>
                <div
                  className="sentiment-score"
                  style={{
                    fontSize: '2rem',
                    fontWeight: '800',
                    color: getSentimentColor(predictions.sentiment.interpretation) === 'green' ? '#059669' :
                           getSentimentColor(predictions.sentiment.interpretation) === 'red' ? '#dc2626' : '#d97706',
                    marginBottom: '0.5rem'
                  }}
                >
                  {predictions.sentiment.interpretation}
                </div>
                <div className="sentiment-confidence" style={{ color: '#6b7280', marginBottom: '1rem' }}>
                  Confianza: {getConfidenceLevel(predictions.sentiment.confidence)} ({Math.round(predictions.sentiment.confidence)}%)
                </div>

                {predictions.sentiment.signals && predictions.sentiment.signals.length > 0 && (
                  <div className="sentiment-signals">
                    {predictions.sentiment.signals.map((signal, index) => (
                      <div key={index} className="signal-item" style={{
                        display: 'flex',
                        justifyContent: 'space-between',
                        padding: '0.5rem',
                        background: 'rgba(0, 0, 0, 0.02)',
                        borderRadius: '6px',
                        marginBottom: '0.25rem',
                        fontSize: '0.85rem'
                      }}>
                        <span>{signal.indicator}</span>
                        <span style={{ fontWeight: '600', color: signal.signal.includes('BULLISH') ? '#059669' : '#dc2626' }}>
                          {signal.signal}
                        </span>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            </div>
          ) : (
            <div className="no-data">Calculando sentimiento...</div>
          )}
        </div>
      </div>
    </>
  );
};
