import React from 'react';
import type { NetworkMetrics, BlockData } from '../EnhancedBlockchainTab';
import { formatNumber } from '../../utils/blockchainUtils';

interface NetworkTabProps {
  networkMetrics: NetworkMetrics | null;
  currentBlock: BlockData | null;
}

export const NetworkTab: React.FC<NetworkTabProps> = ({
  networkMetrics,
  currentBlock
}) => {
  return (
    <div className="network-dashboard">
      <div className="grid-container">
        {/* Hash Rate Analysis */}
        <div className="card">
          <div className="card-header">
            <h3>⚡ Análisis de Hash Rate</h3>
          </div>

          {networkMetrics ? (
            <div className="hashrate-analysis">
              <div className="hashrate-main" style={{ textAlign: 'center', marginBottom: '1.5rem' }}>
                <div style={{ fontSize: '2rem', fontWeight: '700', color: '#4f46e5' }}>
                  {formatNumber(networkMetrics.hashRate / 1e18, 0)} EH/s
                </div>
                <div style={{ color: '#6b7280', marginBottom: '0.5rem' }}>
                  Hash Rate Actual
                </div>
                <div style={{
                  color: networkMetrics.hashRateTrend > 0 ? '#059669' : '#dc2626',
                  fontWeight: '600'
                }}>
                  {networkMetrics.hashRateTrend > 0 ? '↗' : '↘'}
                  {(networkMetrics.hashRateTrend * 100).toFixed(1)}% vs 7d
                </div>
              </div>

              <div className="hashrate-insights">
                <div className="insight" style={{
                  padding: '1rem',
                  background: networkMetrics.hashRateTrend > 0.05 ? 'rgba(5, 150, 105, 0.1)' :
                             networkMetrics.hashRateTrend < -0.1 ? 'rgba(220, 38, 38, 0.1)' : 'rgba(245, 158, 11, 0.1)',
                  borderRadius: '8px',
                  borderLeft: `3px solid ${networkMetrics.hashRateTrend > 0.05 ? '#059669' :
                                             networkMetrics.hashRateTrend < -0.1 ? '#dc2626' : '#f59e0b'}`
                }}>
                  <strong>📊 Interpretación:</strong>
                  <span style={{ display: 'block', marginTop: '0.5rem', fontSize: '0.9rem' }}>
                    {networkMetrics.hashRateTrend > 0.05 ?
                      'Hash rate creciente indica confianza de los mineros y seguridad de red en aumento. Los mineros están invirtiendo más recursos.' :
                      networkMetrics.hashRateTrend < -0.1 ?
                      'Hash rate decreciente puede indicar capitulación de mineros o rebalanceo. Algunos mineros menos eficientes podrían estar saliendo.' :
                      'Hash rate estable, red en equilibrio. La minería se mantiene estable y rentable.'}
                  </span>
                </div>

                {/* Hash Rate Details */}
                <div className="hashrate-details" style={{ marginTop: '1rem' }}>
                  <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(120px, 1fr))', gap: '1rem' }}>
                    <div style={{ textAlign: 'center', padding: '0.75rem', background: 'rgba(0, 0, 0, 0.02)', borderRadius: '8px' }}>
                      <div style={{ fontSize: '1.1rem', fontWeight: '600', color: '#4f46e5' }}>
                        {formatNumber(networkMetrics.hashRate / 1e15, 1)} PH/s
                      </div>
                      <div style={{ fontSize: '0.8rem', color: '#6b7280' }}>En Petahashes</div>
                    </div>

                    <div style={{ textAlign: 'center', padding: '0.75rem', background: 'rgba(0, 0, 0, 0.02)', borderRadius: '8px' }}>
                      <div style={{ fontSize: '1.1rem', fontWeight: '600', color: '#4f46e5' }}>
                        ~{Math.round(networkMetrics.hashRate / 1e18 / 0.11)}
                      </div>
                      <div style={{ fontSize: '0.8rem', color: '#6b7280' }}>Antminer S19 equivalentes</div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          ) : (
            <div className="no-data">Cargando datos de hash rate...</div>
          )}
        </div>

        {/* Difficulty Analysis */}
        <div className="card">
          <div className="card-header">
            <h3>🎯 Ajuste de Dificultad</h3>
          </div>

          {networkMetrics ? (
            <div className="difficulty-analysis">
              <div className="difficulty-main" style={{ textAlign: 'center', marginBottom: '1.5rem' }}>
                <div style={{ fontSize: '1.8rem', fontWeight: '700', color: '#4f46e5' }}>
                  {formatNumber(networkMetrics.difficulty / 1e12, 2)}T
                </div>
                <div style={{ color: '#6b7280', marginBottom: '0.5rem' }}>
                  Dificultad Actual
                </div>
                <div style={{
                  color: networkMetrics.nextDifficultyChange > 0 ? '#dc2626' : '#059669',
                  fontWeight: '600'
                }}>
                  Próximo ajuste: {networkMetrics.nextDifficultyChange > 0 ? '+' : ''}{(networkMetrics.nextDifficultyChange * 100).toFixed(1)}%
                </div>
              </div>

              <div className="difficulty-insights">
                <div className="insight" style={{
                  padding: '1rem',
                  background: 'rgba(79, 70, 229, 0.05)',
                  borderRadius: '8px',
                  borderLeft: '3px solid #4f46e5'
                }}>
                  <strong>🔧 ¿Qué significa?</strong>
                  <span style={{ display: 'block', marginTop: '0.5rem', fontSize: '0.9rem', lineHeight: '1.5' }}>
                    La dificultad se ajusta cada 2016 bloques (~2 semanas) para mantener un tiempo promedio de 10 minutos por bloque.
                    Si los bloques salen más rápido, la dificultad aumenta. Si salen más lento, disminuye.
                  </span>
                </div>

                <div className="difficulty-prediction" style={{ marginTop: '1rem' }}>
                  <div style={{
                    padding: '1rem',
                    background: networkMetrics.nextDifficultyChange > 0 ? 'rgba(220, 38, 38, 0.05)' : 'rgba(5, 150, 105, 0.05)',
                    borderRadius: '8px',
                    border: `1px solid ${networkMetrics.nextDifficultyChange > 0 ? 'rgba(220, 38, 38, 0.2)' : 'rgba(5, 150, 105, 0.2)'}`
                  }}>
                    <strong>
                      {networkMetrics.nextDifficultyChange > 0 ? '📈 Dificultad Aumentando' : '📉 Dificultad Disminuyendo'}
                    </strong>
                    <div style={{ fontSize: '0.9rem', color: '#6b7280', marginTop: '0.5rem' }}>
                      {networkMetrics.nextDifficultyChange > 0 ?
                        'Los bloques han salido más rápido de lo normal. La red se ajustará para mantener los 10 minutos promedio.' :
                        'Los bloques han salido más lento de lo normal. La red facilitará la minería para acelerar el tiempo de bloque.'
                      }
                    </div>
                  </div>
                </div>
              </div>
            </div>
          ) : (
            <div className="no-data">Cargando datos de dificultad...</div>
          )}
        </div>
      </div>

      {/* Mining Economics - Panel principal */}
      <div className="card">
        <div className="card-header">
          <h3>⛏️ Economía de la Minería</h3>
        </div>

        <div className="mining-economics">
          <div className="economics-grid">
            <div className="economics-item">
              <div className="economics-icon">🎁</div>
              <div className="economics-content">
                <div className="economics-value">6.25 BTC</div>
                <div className="economics-label">Recompensa Base</div>
                <div className="economics-desc">Hasta próximo halving (~2028)</div>
              </div>
            </div>

            <div className="economics-item">
              <div className="economics-icon">💰</div>
              <div className="economics-content">
                <div className="economics-value">
                  {currentBlock ? formatNumber(currentBlock.totalFees / 100000000, 2) : '0'} BTC
                </div>
                <div className="economics-label">Fees Último Bloque</div>
                <div className="economics-desc">Comisiones adicionales</div>
              </div>
            </div>

            <div className="economics-item">
              <div className="economics-icon">🔥</div>
              <div className="economics-content">
                <div className="economics-value">
                  ${formatNumber(
                    (6.25 + (currentBlock ? currentBlock.totalFees / 100000000 : 0)) * 50000,
                    0
                  )}
                </div>
                <div className="economics-label">Valor Total Bloque</div>
                <div className="economics-desc">Recompensa + fees en USD (estimado)</div>
              </div>
            </div>

            <div className="economics-item">
              <div className="economics-icon">📅</div>
              <div className="economics-content">
                <div className="economics-value">~4 años</div>
                <div className="economics-label">Próximo Halving</div>
                <div className="economics-desc">Bloque ~1,050,000</div>
              </div>
            </div>

            {/* Additional metrics */}
            <div className="economics-item">
              <div className="economics-icon">⚡</div>
              <div className="economics-content">
                <div className="economics-value">
                  {networkMetrics ? formatNumber(networkMetrics.hashRate / 1e18 * 0.03, 1) : '0'} GW
                </div>
                <div className="economics-label">Consumo Estimado</div>
                <div className="economics-desc">Potencia de la red</div>
              </div>
            </div>

            <div className="economics-item">
              <div className="economics-icon">🌍</div>
              <div className="economics-content">
                <div className="economics-value">
                  ${networkMetrics ?
                    formatNumber((networkMetrics.hashRate / 1e18 * 0.05 * 50000), 0) : '0'}
                </div>
                <div className="economics-label">Costo Eléctrico Diario</div>
                <div className="economics-desc">Estimación global</div>
              </div>
            </div>
          </div>

          {/* Mining Insights */}
          <div className="mining-insights" style={{ marginTop: '2rem' }}>
            <h4 style={{ marginBottom: '1rem', color: '#374151' }}>💡 Insights de Minería:</h4>
            <div className="insights-grid" style={{ display: 'grid', gap: '1rem' }}>
              <div className="insight-item" style={{
                padding: '1rem',
                background: 'rgba(245, 158, 11, 0.05)',
                borderRadius: '8px',
                borderLeft: '3px solid #f59e0b'
              }}>
                <strong>💼 Rentabilidad de Mineros:</strong>
                <span style={{ display: 'block', fontSize: '0.9rem', color: '#6b7280', marginTop: '0.25rem' }}>
                  {networkMetrics && networkMetrics.hashRateTrend > 0 ?
                    'Hash rate creciente indica minería rentable. Los mineros están expandiendo operaciones.' :
                    'Monitorear posible presión de venta de mineros si la rentabilidad baja.'}
                </span>
              </div>

              <div className="insight-item" style={{
                padding: '1rem',
                background: 'rgba(16, 185, 129, 0.05)',
                borderRadius: '8px',
                borderLeft: '3px solid #10b981'
              }}>
                <strong>🛡️ Seguridad de la Red:</strong>
                <span style={{ display: 'block', fontSize: '0.9rem', color: '#6b7280', marginTop: '0.25rem' }}>
                  Mayor hash rate = red más segura y costosa de atacar. Un ataque del 51% costaría miles de millones.
                </span>
              </div>

              <div className="insight-item" style={{
                padding: '1rem',
                background: 'rgba(79, 70, 229, 0.05)',
                borderRadius: '8px',
                borderLeft: '3px solid #4f46e5'
              }}>
                <strong>📈 Ciclo del Halving:</strong>
                <span style={{ display: 'block', fontSize: '0.9rem', color: '#6b7280', marginTop: '0.25rem' }}>
                  Cada halving reduce la inflación de Bitcoin a la mitad, creando escasez programada. Históricamente bullish.
                </span>
              </div>

              <div className="insight-item" style={{
                padding: '1rem',
                background: 'rgba(139, 69, 19, 0.05)',
                borderRadius: '8px',
                borderLeft: '3px solid #8b4513'
              }}>
                <strong>🌱 Minería Sostenible:</strong>
                <span style={{ display: 'block', fontSize: '0.9rem', color: '#6b7280', marginTop: '0.25rem' }}>
                  ~50% de la minería usa energías renovables. Bitcoin incentiva el uso eficiente de energía.
                </span>
              </div>
            </div>
          </div>

          {/* Mining Difficulty Chart Concept */}
          <div className="mining-chart" style={{ marginTop: '2rem' }}>
            <h4 style={{ marginBottom: '1rem', color: '#374151' }}>📊 Tendencias Históricas:</h4>
            <div style={{
              padding: '2rem',
              background: 'rgba(0, 0, 0, 0.02)',
              borderRadius: '12px',
              textAlign: 'center',
              border: '2px dashed rgba(0, 0, 0, 0.1)'
            }}>
              <div style={{ fontSize: '3rem', marginBottom: '1rem' }}>📈</div>
              <div style={{ fontSize: '1.1rem', fontWeight: '600', color: '#374151', marginBottom: '0.5rem' }}>
                Gráfico de Dificultad y Hash Rate
              </div>
              <div style={{ fontSize: '0.9rem', color: '#6b7280' }}>
                Aquí se podría implementar un gráfico histórico de la dificultad y hash rate de Bitcoin
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
