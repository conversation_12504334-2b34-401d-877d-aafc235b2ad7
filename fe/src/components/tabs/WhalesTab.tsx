import React from 'react';
import type { WhaleActivity, WhaleAlert } from '../EnhancedBlockchainTab';
import { formatNumber } from '../../utils/blockchainUtils';

interface WhalesTabProps {
  whaleActivity: WhaleActivity | null;
  whaleAlerts: WhaleAlert[];
}

export const WhalesTab: React.FC<WhalesTabProps> = ({
  whaleActivity,
  whaleAlerts
}) => {
  return (
    <div className="whales-dashboard">
      {/* Whale Alerts */}
      <div className="card">
        <div className="card-header">
          <h3>🚨 Alertas de Ballenas</h3>
          <span style={{
            background: whaleAlerts.length > 0 ? 'rgba(239, 68, 68, 0.1)' : 'rgba(16, 185, 129, 0.1)',
            color: whaleAlerts.length > 0 ? '#dc2626' : '#059669',
            padding: '0.25rem 0.75rem',
            borderRadius: '15px',
            fontSize: '0.75rem'
          }}>
            {whaleAlerts.length} alertas activas
          </span>
        </div>

        {whaleAlerts.length > 0 ? (
          <div className="whale-alerts-list">
            {whaleAlerts.slice(0, 5).map((alert, index) => (
              <div key={index} className="whale-alert" style={{
                padding: '1rem',
                background: alert.severity === 'HIGH' ? 'rgba(239, 68, 68, 0.1)' : 'rgba(245, 158, 11, 0.1)',
                border: `1px solid ${alert.severity === 'HIGH' ? 'rgba(239, 68, 68, 0.3)' : 'rgba(245, 158, 11, 0.3)'}`,
                borderRadius: '8px',
                marginBottom: '0.75rem'
              }}>
                <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start' }}>
                  <div>
                    <div style={{ fontWeight: '600', marginBottom: '0.25rem' }}>
                      {alert.message}
                    </div>
                    <div style={{ fontSize: '0.8rem', color: '#6b7280' }}>
                      {new Date(alert.timestamp).toLocaleTimeString()}
                    </div>
                  </div>
                  <span style={{
                    background: alert.severity === 'HIGH' ? '#dc2626' : '#d97706',
                    color: 'white',
                    padding: '0.25rem 0.5rem',
                    borderRadius: '12px',
                    fontSize: '0.7rem'
                  }}>
                    {alert.severity}
                  </span>
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className="no-data">
            <div className="no-data-icon">🐋</div>
            <div>No hay actividad significativa de ballenas</div>
            <div className="no-data-explanation">
              Las transacciones &gt;10 BTC aparecerán aquí automáticamente
            </div>
          </div>
        )}
      </div>

      {/* Whale Statistics */}
      <div className="grid-container">
        <div className="card">
          <div className="card-header">
            <h3>📊 Estadísticas de Ballenas (24h)</h3>
          </div>

          {whaleActivity ? (
            <div className="whale-stats">
              <div className="stat-grid">
                <div className="whale-stat">
                  <div style={{ fontSize: '1.5rem', fontWeight: '700', color: '#4f46e5' }}>
                    {whaleActivity.count || 0}
                  </div>
                  <div style={{ fontSize: '0.9rem', color: '#6b7280' }}>
                    Transacciones &gt;10 BTC
                  </div>
                </div>

                <div className="whale-stat">
                  <div style={{ fontSize: '1.5rem', fontWeight: '700', color: '#4f46e5' }}>
                    {formatNumber(whaleActivity.totalVolume || 0, 0)} BTC
                  </div>
                  <div style={{ fontSize: '0.9rem', color: '#6b7280' }}>
                    Volumen Total
                  </div>
                </div>

                <div className="whale-stat">
                  <div style={{ fontSize: '1.5rem', fontWeight: '700', color: '#4f46e5' }}>
                    ${formatNumber((whaleActivity.totalVolume || 0) * 50000, 0)}
                  </div>
                  <div style={{ fontSize: '0.9rem', color: '#6b7280' }}>
                    Valor USD (estimado)
                  </div>
                </div>
              </div>

              {/* Whale Activity Details */}
              {whaleActivity.largeTransactions && whaleActivity.largeTransactions.length > 0 && (
                <div className="whale-transactions" style={{ marginTop: '1.5rem' }}>
                  <h4 style={{ marginBottom: '1rem', color: '#374151' }}>Transacciones Grandes Recientes:</h4>
                  <div className="transactions-list">
                    {whaleActivity.largeTransactions.slice(0, 3).map((tx, index) => (
                      <div key={index} className="whale-transaction" style={{
                        padding: '1rem',
                        background: 'rgba(79, 70, 229, 0.05)',
                        borderRadius: '8px',
                        marginBottom: '0.75rem',
                        border: '1px solid rgba(79, 70, 229, 0.1)'
                      }}>
                        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start' }}>
                          <div>
                            <div style={{ fontWeight: '600', marginBottom: '0.25rem' }}>
                              {tx.value ? formatNumber(tx.value, 2) : 'N/A'} BTC
                            </div>
                            <div style={{ fontSize: '0.8rem', color: '#6b7280' }}>
                              TXID: {tx.txid ? `${tx.txid.substring(0, 8)}...${tx.txid.substring(tx.txid.length - 4)}` : 'N/A'}
                            </div>
                          </div>
                          <div style={{ textAlign: 'right' }}>
                            <div style={{ fontSize: '0.9rem', color: '#4f46e5', fontWeight: '600' }}>
                              ${tx.valueUSD ? formatNumber(tx.valueUSD, 0) : 'N/A'}
                            </div>
                            <div style={{ fontSize: '0.8rem', color: '#6b7280' }}>
                              {tx.type || 'LARGE_TRANSFER'}
                            </div>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          ) : (
            <div className="no-data">Cargando estadísticas de ballenas...</div>
          )}
        </div>

        <div className="card">
          <div className="card-header">
            <h3>🧠 Interpretación para Trading</h3>
          </div>

          <div className="whale-interpretation">
            <div className="interpretation-list">
              <div className="interpretation-item" style={{
                padding: '1rem',
                background: 'rgba(79, 70, 229, 0.05)',
                borderRadius: '8px',
                borderLeft: '3px solid #4f46e5',
                marginBottom: '1rem'
              }}>
                <strong>🔥 Actividad Alta de Ballenas:</strong>
                <span style={{ display: 'block', fontSize: '0.9rem', color: '#6b7280', marginTop: '0.25rem' }}>
                  Puede indicar movimientos significativos del precio en las próximas horas. Las ballenas suelen mover el mercado.
                </span>
              </div>

              <div className="interpretation-item" style={{
                padding: '1rem',
                background: 'rgba(239, 68, 68, 0.05)',
                borderRadius: '8px',
                borderLeft: '3px solid #dc2626',
                marginBottom: '1rem'
              }}>
                <strong>📉 Transacciones hacia Exchanges:</strong>
                <span style={{ display: 'block', fontSize: '0.9rem', color: '#6b7280', marginTop: '0.25rem' }}>
                  Potencial presión de venta (bearish a corto plazo). Las ballenas podrían estar preparándose para vender.
                </span>
              </div>

              <div className="interpretation-item" style={{
                padding: '1rem',
                background: 'rgba(16, 185, 129, 0.05)',
                borderRadius: '8px',
                borderLeft: '3px solid #10b981',
                marginBottom: '1rem'
              }}>
                <strong>📈 Transacciones desde Exchanges:</strong>
                <span style={{ display: 'block', fontSize: '0.9rem', color: '#6b7280', marginTop: '0.25rem' }}>
                  Acumulación/HODLing (bullish a medio plazo). Las ballenas están retirando Bitcoin para guardarlo.
                </span>
              </div>

              <div className="interpretation-item" style={{
                padding: '1rem',
                background: 'rgba(245, 158, 11, 0.05)',
                borderRadius: '8px',
                borderLeft: '3px solid #f59e0b'
              }}>
                <strong>⚡ Fees Altos en Transacciones Grandes:</strong>
                <span style={{ display: 'block', fontSize: '0.9rem', color: '#6b7280', marginTop: '0.25rem' }}>
                  Urgencia por mover grandes cantidades. Posible reacción a noticias o eventos del mercado.
                </span>
              </div>
            </div>
          </div>

          {/* Whale Activity Indicators */}
          <div className="whale-indicators" style={{ marginTop: '1.5rem' }}>
            <h4 style={{ marginBottom: '1rem', color: '#374151' }}>🎯 Indicadores Clave:</h4>
            <div style={{ display: 'grid', gap: '0.75rem' }}>
              <div style={{
                display: 'flex',
                justifyContent: 'space-between',
                padding: '0.5rem',
                background: 'rgba(0, 0, 0, 0.02)',
                borderRadius: '6px'
              }}>
                <span style={{ fontSize: '0.9rem' }}>Nivel de Actividad:</span>
                <span style={{
                  fontWeight: '600',
                  color: (whaleActivity?.count || 0) > 5 ? '#dc2626' : (whaleActivity?.count || 0) > 2 ? '#d97706' : '#059669'
                }}>
                  {(whaleActivity?.count || 0) > 5 ? 'MUY ALTO' : (whaleActivity?.count || 0) > 2 ? 'ALTO' : 'NORMAL'}
                </span>
              </div>

              <div style={{
                display: 'flex',
                justifyContent: 'space-between',
                padding: '0.5rem',
                background: 'rgba(0, 0, 0, 0.02)',
                borderRadius: '6px'
              }}>
                <span style={{ fontSize: '0.9rem' }}>Impacto Potencial:</span>
                <span style={{
                  fontWeight: '600',
                  color: (whaleActivity?.totalVolume || 0) > 1000 ? '#dc2626' : (whaleActivity?.totalVolume || 0) > 500 ? '#d97706' : '#059669'
                }}>
                  {(whaleActivity?.totalVolume || 0) > 1000 ? 'ALTO' : (whaleActivity?.totalVolume || 0) > 500 ? 'MEDIO' : 'BAJO'}
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
