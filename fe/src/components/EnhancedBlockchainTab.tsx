import { useState, useEffect } from 'react';
import { OverviewTab } from './tabs/OverviewTab';
import { PredictionsTab } from './tabs/PredictionsTab';
import { WhalesTab } from './tabs/WhalesTab';
import { NetworkTab } from './tabs/NetworkTab';
import { ChartsTab } from './tabs/ChartsTab';

// ============================================
// INTERFACES DE TIPOS (SIN CAMBIOS)
// ============================================

export interface BlockData {
  height: number;
  hash: string;
  timestamp: number;
  txCount: number;
  totalFees: number;
  size: number;
  weight?: number;
  difficulty?: string;
  source: string;
}

export interface MempoolData {
  count: number;
  totalFees: number;
  vsize: number;
  timestamp: number;
}

export interface Transaction {
  txid: string;
  fee: number;
  vsize: number;
  feeRate: number;
  value: number;
}



export interface NetworkMetrics {
  hashRate: number;
  hashRateTrend: number;
  difficulty: number;
  nextDifficultyChange: number;
  recommendedFees: any;
  timestamp: number;
}

export interface WhaleActivity {
  largeTransactions: any[];
  totalVolume: number;
  count: number;
  alerts: WhaleAlert[];
}

export interface WhaleAlert {
  type: string;
  message: string;
  severity: 'HIGH' | 'MEDIUM' | 'LOW';
  txid: string;
  timestamp: number;
}

export interface Predictions {
  fees?: {
    currentRecommended: number;
    nextHour: number;
    next6Hours: number;
    next24Hours: number;
    interpretation?: {
      congestionLevel: string;
      recommendation: string;
      confidence: number;
    };
  };
  sentiment?: {
    score: number;
    interpretation: 'BULLISH' | 'BEARISH' | 'NEUTRAL';
    confidence: number;
    signals: Array<{
      indicator: string;
      signal: string;
      weight: number;
    }>;
    timestamp: number;
  };
}

interface EnhancedBlockchainTabProps {
  currentBlock: BlockData | null;
  mempoolData: MempoolData | null;
  recentTransactions: Transaction[];
  networkMetrics: NetworkMetrics | null;
  whaleActivity: WhaleActivity | null;
  predictions: Predictions | null;
  refreshBlockchain: () => void;
  showEducationalPanel: boolean;
  setShowEducationalPanel: (show: boolean) => void;
}

// ============================================
// COMPONENTE PRINCIPAL
// ============================================

const EnhancedBlockchainTab: React.FC<EnhancedBlockchainTabProps> = ({
  currentBlock,
  mempoolData,
  recentTransactions,
  networkMetrics,
  whaleActivity,
  predictions,
  refreshBlockchain,
  showEducationalPanel,
  setShowEducationalPanel
}) => {
  // Estado actualizado para incluir el nuevo tab de gráficos
  const [activeSubTab, setActiveSubTab] = useState<'overview' | 'charts' | 'predictions' | 'whales' | 'network'>('overview');
  const [whaleAlerts, setWhaleAlerts] = useState<WhaleAlert[]>([]);

  // Update whale alerts when new whale activity comes in
  useEffect(() => {
    if (whaleActivity?.alerts) {
      setWhaleAlerts(prev => [...whaleActivity.alerts, ...prev].slice(0, 10));
    }
  }, [whaleActivity?.alerts]);

  // Props compartidos para todos los tabs
  const sharedProps = {
    currentBlock,
    mempoolData,
    recentTransactions,
    networkMetrics,
    whaleActivity,
    predictions,
    whaleAlerts
  };

  return (
    <div className="tab-content">
      {/* Enhanced Header */}
      <div className="section-header">
        <h2>🔮 Bitcoin Blockchain Analytics</h2>
        <div className="action-buttons">
          <button onClick={refreshBlockchain} className="btn-secondary">
            🔄 Actualizar Datos
          </button>
          <button
            onClick={() => setShowEducationalPanel(!showEducationalPanel)}
            className="btn-educational"
          >
            📚 ¿Qué significa esto?
          </button>
        </div>
      </div>

      {/* Enhanced Sub-navigation con nuevo tab de gráficos */}
      <div className="sub-tab-navigation">
        <button
          className={`tab-btn ${activeSubTab === 'overview' ? 'active' : ''}`}
          onClick={() => setActiveSubTab('overview')}
          title="Vista general con métricas principales"
        >
          📊 Vista General
        </button>

        <button
          className={`tab-btn ${activeSubTab === 'charts' ? 'active' : ''}`}
          onClick={() => setActiveSubTab('charts')}
          title="Gráficos históricos y análisis visual"
        >
          📈 Gráficos
        </button>

        <button
          className={`tab-btn ${activeSubTab === 'predictions' ? 'active' : ''}`}
          onClick={() => setActiveSubTab('predictions')}
          title="Predicciones de fees y análisis de sentimiento"
        >
          🔮 Predicciones
        </button>

        <button
          className={`tab-btn ${activeSubTab === 'whales' ? 'active' : ''}`}
          onClick={() => setActiveSubTab('whales')}
          title="Monitor de transacciones grandes (>10 BTC)"
        >
          🐋 Monitor Ballenas
        </button>

        <button
          className={`tab-btn ${activeSubTab === 'network' ? 'active' : ''}`}
          onClick={() => setActiveSubTab('network')}
          title="Hash rate, dificultad y economía de minería"
        >
          ⚡ Red & Minería
        </button>
      </div>

      {/* Indicador de tab activo */}
      <div className="tab-indicator">
        <div className="tab-description">
          {activeSubTab === 'overview' && (
            <span>📋 Resumen ejecutivo con las métricas más importantes</span>
          )}
          {activeSubTab === 'charts' && (
            <span>📊 Visualización histórica y tendencias de datos blockchain</span>
          )}
          {activeSubTab === 'predictions' && (
            <span>🎯 Predicciones algorítmicas basadas en patrones históricos</span>
          )}
          {activeSubTab === 'whales' && (
            <span>🐋 Seguimiento de movimientos de Bitcoin institucionales</span>
          )}
          {activeSubTab === 'network' && (
            <span>⛏️ Análisis de la infraestructura y economía de la red</span>
          )}
        </div>
      </div>

      {/* Renderizar el tab activo */}
      <div className="tab-content-wrapper">
        {activeSubTab === 'overview' && <OverviewTab {...sharedProps} />}
        {activeSubTab === 'charts' && <ChartsTab {...sharedProps} />}
        {activeSubTab === 'predictions' && <PredictionsTab {...sharedProps} />}
        {activeSubTab === 'whales' && <WhalesTab {...sharedProps} />}
        {activeSubTab === 'network' && <NetworkTab {...sharedProps} />}
      </div>

      {/* Información de conectividad y estado */}
      <div className="connection-info">
        <div className="connection-badges">
          <span className={`badge ${currentBlock ? 'connected' : 'disconnected'}`}>
            🧱 Bloques: {currentBlock ? 'Activo' : 'Desconectado'}
          </span>
          <span className={`badge ${mempoolData ? 'connected' : 'disconnected'}`}>
            📊 Mempool: {mempoolData ? 'Activo' : 'Desconectado'}
          </span>
          <span className={`badge ${networkMetrics ? 'connected' : 'disconnected'}`}>
            ⚡ Red: {networkMetrics ? 'Activo' : 'Desconectado'}
          </span>
          <span className={`badge ${whaleActivity ? 'connected' : 'disconnected'}`}>
            🐋 Ballenas: {whaleActivity ? 'Activo' : 'Desconectado'}
          </span>
        </div>

        <div className="data-freshness">
          <span className="freshness-label">Última actualización:</span>
          <span className="freshness-time">
            {currentBlock ?
              new Date(currentBlock.timestamp * 1000).toLocaleTimeString('es-ES') :
              'N/A'
            }
          </span>
        </div>
      </div>
    </div>
  );
};

export default EnhancedBlockchainTab;

// ============================================
// ESTILOS CSS ADICIONALES
// ============================================

export const enhancedBlockchainTabStyles = `
.tab-content-wrapper {
  animation: fadeIn 0.4s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.tab-indicator {
  margin: 1rem 0 2rem 0;
  text-align: center;
}

.tab-description {
  display: inline-flex;
  align-items: center;
  padding: 0.75rem 1.5rem;
  background: rgba(79, 70, 229, 0.1);
  color: #4f46e5;
  border-radius: 20px;
  font-size: 0.9rem;
  font-weight: 500;
  border: 1px solid rgba(79, 70, 229, 0.2);
}

.connection-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 2rem;
  padding: 1rem;
  background: rgba(0, 0, 0, 0.02);
  border-radius: 12px;
  border: 1px solid rgba(0, 0, 0, 0.05);
  flex-wrap: wrap;
  gap: 1rem;
}

.connection-badges {
  display: flex;
  gap: 0.75rem;
  flex-wrap: wrap;
}

.badge {
  padding: 0.25rem 0.75rem;
  border-radius: 15px;
  font-size: 0.8rem;
  font-weight: 600;
  border: 1px solid;
}

.badge.connected {
  background: rgba(16, 185, 129, 0.1);
  color: #059669;
  border-color: rgba(16, 185, 129, 0.3);
}

.badge.disconnected {
  background: rgba(107, 114, 128, 0.1);
  color: #6b7280;
  border-color: rgba(107, 114, 128, 0.3);
}

.data-freshness {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.9rem;
}

.freshness-label {
  color: #6b7280;
}

.freshness-time {
  color: #374151;
  font-weight: 600;
  font-family: 'Courier New', monospace;
}

/* Enhanced tab buttons */
.sub-tab-navigation .tab-btn {
  position: relative;
  transition: all 0.3s ease;
}

.sub-tab-navigation .tab-btn:hover:not(.active) {
  background: rgba(79, 70, 229, 0.1);
  color: #4f46e5;
  transform: translateY(-1px);
}

.sub-tab-navigation .tab-btn.active {
  background: linear-gradient(45deg, #4f46e5, #7c3aed);
  color: white;
  box-shadow: 0 4px 15px rgba(79, 70, 229, 0.3);
  transform: translateY(-1px);
}

.sub-tab-navigation .tab-btn.active::after {
  content: '';
  position: absolute;
  bottom: -8px;
  left: 50%;
  transform: translateX(-50%);
  width: 0;
  height: 0;
  border-left: 6px solid transparent;
  border-right: 6px solid transparent;
  border-bottom: 6px solid rgba(79, 70, 229, 0.1);
}

/* Responsive enhancements */
@media (max-width: 768px) {
  .connection-info {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }

  .connection-badges {
    width: 100%;
    justify-content: center;
  }

  .data-freshness {
    align-self: center;
  }

  .tab-indicator {
    margin: 0.75rem 0 1.5rem 0;
  }

  .tab-description {
    padding: 0.5rem 1rem;
    font-size: 0.85rem;
    text-align: center;
  }
}

@media (max-width: 480px) {
  .sub-tab-navigation {
    grid-template-columns: 1fr 1fr;
    gap: 0.5rem;
  }

  .sub-tab-navigation .tab-btn {
    padding: 0.75rem 0.5rem;
    font-size: 0.8rem;
    text-align: center;
  }

  .connection-badges {
    grid-template-columns: repeat(2, 1fr);
    display: grid;
    gap: 0.5rem;
    width: 100%;
  }

  .badge {
    text-align: center;
  }
}

/* Enhanced animations */
.tab-content-wrapper > * {
  animation: slideInUp 0.5s ease-out;
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Loading states for tabs */
.tab-loading {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 300px;
  flex-direction: column;
  gap: 1rem;
}

.tab-loading-spinner {
  font-size: 2rem;
  animation: spin 2s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.tab-loading-text {
  color: #6b7280;
  font-style: italic;
}
`;
