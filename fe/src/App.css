/* ============================================ */
/* GLOBAL STYLES */
/* ============================================ */

* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Helvetica Neue', sans-serif;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  min-height: 100vh;
  color: #333;
}

.app {
  max-width: 1400px;
  margin: 0 auto;
  padding: 1rem;
  min-height: 100vh;
}

/* ============================================ */
/* EDUCATIONAL PANEL STYLES */
/* ============================================ */

.educational-toggle {
  position: fixed;
  top: 20px;
  left: 20px;
  z-index: 1000;
}

.educational-btn {
  background: linear-gradient(45deg, #4f46e5, #7c3aed);
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 25px;
  font-size: 0.9rem;
  font-weight: 600;
  cursor: pointer;
  box-shadow: 0 4px 15px rgba(79, 70, 229, 0.3);
  transition: all 0.3s ease;
}

.educational-btn:hover {
  background: linear-gradient(45deg, #4338ca, #6d28d9);
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(79, 70, 229, 0.4);
}

.educational-panel {
  position: fixed;
  top: 20px;
  left: 20px;
  width: 500px;
  max-width: 90vw;
  max-height: 80vh;
  background: rgba(255, 255, 255, 0.98);
  backdrop-filter: blur(20px);
  border-radius: 20px;
  z-index: 1001;
  overflow: hidden;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.educational-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem;
  background: linear-gradient(45deg, #4f46e5, #7c3aed);
  color: white;
}

.educational-header h3 {
  margin: 0;
  font-size: 1.1rem;
  font-weight: 700;
}

.educational-close {
  background: none;
  border: none;
  color: white;
  font-size: 1.5rem;
  cursor: pointer;
  padding: 0.5rem;
  border-radius: 8px;
  transition: background 0.2s ease;
}

.educational-close:hover {
  background: rgba(255, 255, 255, 0.2);
}

.educational-content {
  max-height: calc(80vh - 80px);
  overflow-y: auto;
  padding: 0;
}

.educational-section {
  padding: 1.5rem;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

.educational-section:last-child {
  border-bottom: none;
}

.educational-section h4 {
  margin: 0 0 1rem 0;
  color: #4f46e5;
  font-size: 1rem;
  font-weight: 700;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.educational-section p {
  margin-bottom: 0.75rem;
  color: #374151;
  line-height: 1.6;
}

.educational-section ul {
  margin: 0 0 1rem 1rem;
  color: #374151;
}

.educational-section li {
  margin-bottom: 0.5rem;
  line-height: 1.5;
}

.educational-section li strong {
  color: #1f2937;
  font-weight: 600;
}

.analogy-box {
  background: linear-gradient(45deg, #fef3c7, #fde68a);
  border-left: 4px solid #f59e0b;
  padding: 1rem;
  margin: 1rem 0;
  border-radius: 8px;
  font-style: italic;
  color: #92400e;
}

.process-flow {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
  margin: 1rem 0;
}

.flow-step {
  background: linear-gradient(45deg, #e0e7ff, #c7d2fe);
  color: #3730a3;
  padding: 0.75rem 1.5rem;
  border-radius: 20px;
  font-weight: 600;
  text-align: center;
  min-width: 200px;
  box-shadow: 0 2px 8px rgba(79, 70, 229, 0.1);
}

.flow-arrow {
  font-size: 1.5rem;
  color: #4f46e5;
  font-weight: bold;
}

/* ============================================ */
/* HEADER SECTION */
/* ============================================ */

.header {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 16px;
  padding: 1.5rem;
  margin-bottom: 1.5rem;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.header h1 {
  text-align: center;
  font-size: 2.5rem;
  font-weight: 700;
  background: linear-gradient(45deg, #f7931e, #ff6b35);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin-bottom: 1rem;
}

/* ============================================ */
/* CONNECTION STATUS */
/* ============================================ */

.connection-status {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 1.5rem;
  flex-wrap: wrap;
  margin-bottom: 1.5rem;
  padding: 0.75rem;
  background: rgba(0, 0, 0, 0.05);
  border-radius: 12px;
}

.status-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-weight: 600;
  font-size: 0.9rem;
  transition: all 0.3s ease;
}

.status-item.connected {
  background: rgba(34, 197, 94, 0.1);
  color: #15803d;
  border: 1px solid rgba(34, 197, 94, 0.3);
}

.status-item.disconnected {
  background: rgba(239, 68, 68, 0.1);
  color: #dc2626;
  border: 1px solid rgba(239, 68, 68, 0.3);
}

.last-update {
  color: #6b7280;
  font-size: 0.85rem;
  padding: 0.5rem 1rem;
  background: rgba(0, 0, 0, 0.05);
  border-radius: 20px;
}

.error-message {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  background: rgba(239, 68, 68, 0.1);
  border: 1px solid rgba(239, 68, 68, 0.3);
  border-radius: 12px;
  margin-bottom: 1rem;
  color: #dc2626;
  font-weight: 500;
  flex-wrap: wrap;
}

.retry-btn {
  padding: 0.5rem 1rem;
  background: #dc2626;
  color: white;
  border: none;
  border-radius: 8px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 0.9rem;
}

.retry-btn:hover {
  background: #b91c1c;
  transform: translateY(-2px);
}

/* ============================================ */
/* TAB NAVIGATION */
/* ============================================ */

.tab-navigation {
  display: flex;
  justify-content: center;
  gap: 0.5rem;
}

.tab-btn {
  padding: 0.75rem 2rem;
  border: none;
  border-radius: 30px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  background: rgba(0, 0, 0, 0.1);
  color: #6b7280;
}

.tab-btn:hover {
  background: rgba(0, 0, 0, 0.15);
  transform: translateY(-2px);
}

.tab-btn.active {
  background: linear-gradient(45deg, #f7931e, #ff6b35);
  color: white;
  box-shadow: 0 4px 15px rgba(247, 147, 30, 0.3);
}

/* ============================================ */
/* TAB CONTENT */
/* ============================================ */

.tab-content {
  animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(20px); }
  to { opacity: 1; transform: translateY(0); }
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
  flex-wrap: wrap;
  gap: 1rem;
}

.section-header h2 {
  font-size: 1.8rem;
  font-weight: 700;
  color: white;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.action-buttons {
  display: flex;
  gap: 0.75rem;
}

.btn-secondary, .btn-educational {
  padding: 0.5rem 1rem;
  background: rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 8px;
  color: white;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.btn-educational {
  background: rgba(79, 70, 229, 0.3);
  border-color: rgba(79, 70, 229, 0.5);
}

.btn-secondary:hover, .btn-educational:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

/* ============================================ */
/* CARDS */
/* ============================================ */

.price-card, .card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(15px);
  border-radius: 20px;
  padding: 1.5rem;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  margin-bottom: 1.5rem;
}

.loading-card {
  background: rgba(255, 255, 255, 0.8);
  border-radius: 20px;
  padding: 3rem;
  text-align: center;
  color: #6b7280;
  font-style: italic;
  margin-bottom: 1.5rem;
}

.loading-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
}

.loading-spinner {
  font-size: 2rem;
  animation: spin 2s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.loading-explanation {
  font-size: 0.9rem;
  color: #9ca3af;
  max-width: 300px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.card-header h3 {
  font-size: 1.3rem;
  font-weight: 600;
  color: #374151;
}

.header-badges {
  display: flex;
  gap: 0.5rem;
}

.source-badge, .block-badge {
  background: linear-gradient(45deg, #667eea, #764ba2);
  color: white;
  padding: 0.25rem 0.75rem;
  border-radius: 15px;
  font-size: 0.75rem;
  font-weight: 600;
}

.block-badge {
  background: linear-gradient(45deg, #f59e0b, #d97706);
}

.help-tooltip {
  background: #6b7280;
  color: white;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.7rem;
  cursor: help;
  font-weight: bold;
}

/* ============================================ */
/* ENHANCED BLOCKCHAIN STYLES */
/* ============================================ */

.blockchain-card {
  border-left: 4px solid #f59e0b;
}

.block-display {
  margin: 1.5rem 0;
}

.block-main-info {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 2rem;
  margin-bottom: 2rem;
  align-items: center;
}

.block-height-section {
  text-align: center;
}

.block-height {
  font-size: 3.5rem;
  font-weight: 800;
  color: #f59e0b;
  margin-bottom: 0.5rem;
}

.block-subtitle {
  font-size: 1.1rem;
  color: #6b7280;
  font-weight: 600;
  margin-bottom: 0.75rem;
}

.block-explanation {
  font-size: 0.9rem;
  color: #9ca3af;
  font-style: italic;
  padding: 0.75rem;
  background: rgba(245, 158, 11, 0.1);
  border-radius: 8px;
  border-left: 3px solid #f59e0b;
}

.block-time-info {
  text-align: center;
  padding: 1rem;
  background: rgba(0, 0, 0, 0.05);
  border-radius: 12px;
}

.block-time {
  font-size: 1.1rem;
  font-weight: 600;
  color: #374151;
  margin-bottom: 0.5rem;
}

.time-ago {
  font-size: 0.9rem;
  color: #6b7280;
}

.block-stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
  margin: 1.5rem 0;
}

.block-stat {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  padding: 1.25rem;
  background: rgba(245, 158, 11, 0.05);
  border-radius: 12px;
  border: 1px solid rgba(245, 158, 11, 0.1);
  transition: all 0.3s ease;
}

.block-stat:hover {
  background: rgba(245, 158, 11, 0.1);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(245, 158, 11, 0.2);
}

.stat-icon {
  font-size: 1.5rem;
  margin-top: 0.25rem;
}

.stat-content {
  flex: 1;
}

.stat-value {
  font-size: 1.25rem;
  font-weight: 700;
  color: #1f2937;
  display: block;
  margin-bottom: 0.25rem;
}

.stat-label {
  font-weight: 600;
  color: #374151;
  display: block;
  margin-bottom: 0.5rem;
}

.stat-explanation {
  font-size: 0.85rem;
  color: #6b7280;
  line-height: 1.4;
}

.block-hash-section {
  margin-top: 2rem;
  padding: 1.5rem;
  background: rgba(0, 0, 0, 0.03);
  border-radius: 12px;
  border: 1px solid rgba(0, 0, 0, 0.1);
}

.hash-header {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 1rem;
}

.hash-icon {
  font-size: 1.25rem;
}

.hash-label {
  font-weight: 600;
  color: #374151;
}

.hash-value {
  font-family: 'Courier New', monospace;
  font-size: 0.85rem;
  color: #374151;
  word-break: break-all;
  background: rgba(0, 0, 0, 0.05);
  padding: 0.75rem;
  border-radius: 8px;
  margin-bottom: 0.75rem;
  border-left: 3px solid #f59e0b;
}

.hash-explanation {
  font-size: 0.9rem;
  color: #6b7280;
  font-style: italic;
  line-height: 1.5;
}

/* ============================================ */
/* ENHANCED MEMPOOL STYLES */
/* ============================================ */

.mempool-enhanced {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.mempool-status-indicator {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  background: rgba(0, 0, 0, 0.02);
  border-radius: 12px;
  border: 1px solid rgba(0, 0, 0, 0.1);
}

.status-circle {
  width: 16px;
  height: 16px;
  border-radius: 50%;
  animation: pulse 2s infinite;
}

.status-circle.green { background: #10b981; }
.status-circle.yellow { background: #f59e0b; }
.status-circle.orange { background: #f97316; }
.status-circle.red { background: #ef4444; }

.status-info {
  flex: 1;
}

.status-title {
  font-weight: 700;
  font-size: 1.1rem;
  color: #1f2937;
  margin-bottom: 0.25rem;
}

.status-description {
  color: #6b7280;
  font-size: 0.9rem;
}

.mempool-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
}

.mempool-stat {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  padding: 1.25rem;
  background: rgba(245, 158, 11, 0.05);
  border-radius: 12px;
  border: 1px solid rgba(245, 158, 11, 0.1);
  transition: all 0.3s ease;
}

.mempool-stat:hover {
  background: rgba(245, 158, 11, 0.1);
  transform: translateY(-2px);
}

.mempool-insights {
  background: linear-gradient(135deg, rgba(79, 70, 229, 0.05), rgba(124, 58, 237, 0.05));
  border: 1px solid rgba(79, 70, 229, 0.1);
  border-radius: 12px;
  padding: 1.5rem;
}

.insight-header {
  font-weight: 700;
  color: #4f46e5;
  margin-bottom: 1rem;
  font-size: 1.1rem;
}

.insights-grid {
  display: grid;
  gap: 1rem;
}

.insight-item {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  padding: 1rem;
  background: rgba(255, 255, 255, 0.7);
  border-radius: 8px;
  border-left: 3px solid #4f46e5;
}

.insight-item strong {
  color: #1f2937;
  font-weight: 600;
}

.insight-item span {
  color: #6b7280;
  font-size: 0.9rem;
}

/* ============================================ */
/* ENHANCED TRANSACTIONS STYLES */
/* ============================================ */

.transactions-enhanced {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.transactions-legend {
  display: flex;
  justify-content: center;
  gap: 1.5rem;
  flex-wrap: wrap;
  padding: 0.75rem;
  background: rgba(0, 0, 0, 0.02);
  border-radius: 8px;
  margin-bottom: 1rem;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.85rem;
  color: #6b7280;
}

.legend-color {
  width: 12px;
  height: 12px;
  border-radius: 3px;
}

.fee-high { background: #ef4444; }
.fee-medium { background: #f59e0b; }
.fee-low { background: #10b981; }

.transactions-list {
  max-height: 500px;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.transaction-row.enhanced {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  padding: 1.25rem;
  border-radius: 12px;
  border-left: 4px solid transparent;
  transition: all 0.3s ease;
}

.transaction-row.enhanced.fee-high {
  background: rgba(239, 68, 68, 0.05);
  border-left-color: #ef4444;
}

.transaction-row.enhanced.fee-medium {
  background: rgba(245, 158, 11, 0.05);
  border-left-color: #f59e0b;
}

.transaction-row.enhanced.fee-low {
  background: rgba(16, 185, 129, 0.05);
  border-left-color: #10b981;
}

.transaction-row.enhanced:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.tx-main-info {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: 1rem;
  flex-wrap: wrap;
}

.tx-id-section {
  flex: 1;
  min-width: 200px;
}

.tx-id {
  font-family: 'Courier New', monospace;
  font-weight: 600;
  color: #1f2937;
  font-size: 0.9rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.tx-icon {
  font-size: 1rem;
}

.tx-id-explanation {
  font-size: 0.75rem;
  color: #9ca3af;
  margin-top: 0.25rem;
}

.tx-value-section {
  text-align: right;
}

.tx-value {
  font-weight: 700;
  font-size: 1.1rem;
  color: #1f2937;
  margin-bottom: 0.25rem;
}

.tx-value-usd {
  font-size: 0.85rem;
  color: #6b7280;
}

.tx-fee-info {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 1rem;
  align-items: center;
}

.fee-rate-section, .fee-total-section, .tx-size-section {
  text-align: center;
  padding: 0.75rem;
  background: rgba(0, 0, 0, 0.03);
  border-radius: 8px;
}

.fee-rate {
  font-weight: 700;
  color: #1f2937;
  font-size: 1rem;
  margin-bottom: 0.25rem;
}

.fee-explanation {
  font-size: 0.75rem;
  color: #6b7280;
  font-style: italic;
}

.fee-total {
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 0.25rem;
}

.fee-total-usd {
  font-size: 0.75rem;
  color: #6b7280;
}

.tx-size {
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 0.25rem;
}

.tx-size-explanation {
  font-size: 0.75rem;
  color: #6b7280;
}

.tx-priority-indicator {
  display: flex;
  justify-content: center;
}

.priority-badge {
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 600;
  text-align: center;
}

.priority-badge.priority-high {
  background: rgba(239, 68, 68, 0.15);
  color: #dc2626;
  border: 1px solid rgba(239, 68, 68, 0.3);
}

.priority-badge.priority-medium {
  background: rgba(245, 158, 11, 0.15);
  color: #d97706;
  border: 1px solid rgba(245, 158, 11, 0.3);
}

.priority-badge.priority-low {
  background: rgba(16, 185, 129, 0.15);
  color: #047857;
  border: 1px solid rgba(16, 185, 129, 0.3);
}

.transactions-insights {
  background: linear-gradient(135deg, rgba(79, 70, 229, 0.05), rgba(124, 58, 237, 0.05));
  border: 1px solid rgba(79, 70, 229, 0.1);
  border-radius: 12px;
  padding: 1.5rem;
  margin-top: 1rem;
}

.insights-list {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.insight {
  padding: 0.75rem;
  background: rgba(255, 255, 255, 0.7);
  border-radius: 8px;
  border-left: 3px solid #4f46e5;
  font-size: 0.9rem;
  line-height: 1.4;
}

.insight strong {
  color: #1f2937;
  font-weight: 600;
}

/* ============================================ */
/* BLOCKCHAIN INSIGHTS SECTION */
/* ============================================ */

.blockchain-insights-section {
  margin-top: 1.5rem;
}

.insights-dashboard {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.insight-card {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  padding: 1.5rem;
  background: linear-gradient(135deg, rgba(79, 70, 229, 0.05), rgba(124, 58, 237, 0.05));
  border: 1px solid rgba(79, 70, 229, 0.1);
  border-radius: 12px;
  transition: all 0.3s ease;
}

.insight-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(79, 70, 229, 0.15);
  background: linear-gradient(135deg, rgba(79, 70, 229, 0.08), rgba(124, 58, 237, 0.08));
}

.insight-icon {
  font-size: 2rem;
  margin-top: 0.25rem;
}

.insight-content {
  flex: 1;
}

.insight-title {
  font-weight: 600;
  color: #374151;
  font-size: 0.9rem;
  margin-bottom: 0.5rem;
  text-transform: uppercase;
  letter-spacing: 0.025em;
}

.insight-value {
  font-size: 1.5rem;
  font-weight: 700;
  color: #4f46e5;
  margin-bottom: 0.5rem;
}

.insight-description {
  font-size: 0.85rem;
  color: #6b7280;
  line-height: 1.4;
}

.blockchain-education {
  background: linear-gradient(135deg, rgba(16, 185, 129, 0.05), rgba(5, 150, 105, 0.05));
  border: 1px solid rgba(16, 185, 129, 0.2);
  border-radius: 12px;
  padding: 2rem;
}

.education-header {
  font-size: 1.2rem;
  font-weight: 700;
  color: #059669;
  margin-bottom: 1.5rem;
  text-align: center;
}

.education-facts {
  display: grid;
  gap: 1.5rem;
}

.fact {
  padding: 1.25rem;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 10px;
  border-left: 4px solid #10b981;
  line-height: 1.6;
  color: #374151;
}

.fact strong {
  color: #059669;
  font-weight: 700;
}



.price-display {
  text-align: center;
  margin: 1.5rem 0;
}

.main-price {
  font-size: 3.5rem;
  font-weight: 800;
  color: #1f2937;
  margin-bottom: 0.5rem;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.price-change {
  font-size: 1.3rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

.price-change.positive {
  color: #059669;
}

.price-change.negative {
  color: #dc2626;
}

.price-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
  margin-top: 1.5rem;
  padding-top: 1.5rem;
  border-top: 1px solid rgba(0, 0, 0, 0.1);
}

.stat-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem;
  background: rgba(0, 0, 0, 0.02);
  border-radius: 8px;
}

.stat-item .label {
  color: #6b7280;
  font-weight: 500;
}

.stat-item .value {
  font-weight: 600;
  color: #1f2937;
}

/* ============================================ */
/* GRID LAYOUT */
/* ============================================ */

.grid-container {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 1.5rem;
}

/* ============================================ */
/* ORDER BOOK */
/* ============================================ */

.orderbook {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.asks, .bids {
  flex: 1;
}

.asks h4, .bids h4 {
  margin-bottom: 0.5rem;
  font-size: 1rem;
  font-weight: 600;
}

.asks h4 {
  color: #dc2626;
}

.bids h4 {
  color: #059669;
}

.order-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.4rem 0.75rem;
  margin: 0.25rem 0;
  border-radius: 6px;
  font-size: 0.9rem;
  font-family: 'Courier New', monospace;
}

.order-row.ask {
  background: rgba(220, 38, 38, 0.08);
  border-left: 3px solid #dc2626;
}

.order-row.bid {
  background: rgba(5, 150, 105, 0.08);
  border-left: 3px solid #059669;
}

.order-row .price {
  font-weight: 600;
  color: #374151;
}

.order-row .quantity {
  color: #6b7280;
  font-size: 0.85rem;
}

.spread {
  text-align: center;
  padding: 0.75rem;
  background: rgba(0, 0, 0, 0.05);
  border-radius: 8px;
  font-weight: 600;
  color: #374151;
  border: 1px dashed rgba(0, 0, 0, 0.1);
}

/* ============================================ */
/* TRADES */
/* ============================================ */

.trades-list {
  max-height: 400px;
  overflow-y: auto;
}

.trade-row {
  display: grid;
  grid-template-columns: auto 1fr auto auto;
  gap: 1rem;
  align-items: center;
  padding: 0.75rem;
  margin: 0.5rem 0;
  border-radius: 8px;
  font-size: 0.9rem;
  transition: all 0.2s ease;
}

.trade-row:hover {
  background: rgba(0, 0, 0, 0.05);
  transform: translateX(4px);
}

.trade-row.buy {
  border-left: 3px solid #059669;
  background: rgba(5, 150, 105, 0.05);
}

.trade-row.sell {
  border-left: 3px solid #dc2626;
  background: rgba(220, 38, 38, 0.05);
}

.trade-row .time {
  color: #6b7280;
  font-size: 0.8rem;
}

.trade-row .price {
  font-family: 'Courier New', monospace;
  font-weight: 600;
  color: #374151;
}

.trade-row .quantity {
  font-family: 'Courier New', monospace;
  color: #6b7280;
  font-size: 0.85rem;
}

.trade-row .side {
  font-weight: 600;
  font-size: 0.8rem;
  padding: 0.25rem 0.5rem;
  border-radius: 12px;
}

.side.buy {
  background: rgba(5, 150, 105, 0.1);
  color: #059669;
}

.side.sell {
  background: rgba(220, 38, 38, 0.1);
  color: #dc2626;
}

/* ============================================ */
/* NO DATA STATE */
/* ============================================ */

.no-data {
  text-align: center;
  color: #6b7280;
  font-style: italic;
  padding: 2rem;
  background: rgba(0, 0, 0, 0.02);
  border-radius: 8px;
  border: 1px dashed rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.75rem;
}

.no-data-icon {
  font-size: 2rem;
  opacity: 0.7;
}

.no-data-explanation {
  font-size: 0.85rem;
  color: #9ca3af;
  max-width: 300px;
  line-height: 1.4;
}

/* ============================================ */
/* DEBUG PANEL (TEMPORARY) */
/* ============================================ */

.debug-toggle {
  position: fixed;
  bottom: 20px;
  right: 20px;
  z-index: 1000;
}

.debug-btn {
  background: #dc2626;
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-size: 0.8rem;
  cursor: pointer;
  box-shadow: 0 4px 15px rgba(220, 38, 38, 0.3);
  transition: all 0.3s ease;
}

.debug-btn:hover {
  background: #b91c1c;
  transform: translateY(-2px);
}

.debug-panel {
  position: fixed;
  top: 20px;
  right: 20px;
  width: 400px;
  max-height: 80vh;
  background: rgba(0, 0, 0, 0.95);
  color: #00ff00;
  border-radius: 12px;
  z-index: 1001;
  overflow: hidden;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.5);
  font-family: 'Courier New', monospace;
}

.debug-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  background: rgba(220, 38, 38, 0.8);
  color: white;
}

.debug-header h3 {
  margin: 0;
  font-size: 1rem;
}

.debug-close {
  background: none;
  border: none;
  color: white;
  font-size: 1.2rem;
  cursor: pointer;
  padding: 0.25rem;
  border-radius: 4px;
  transition: background 0.2s ease;
}

.debug-close:hover {
  background: rgba(255, 255, 255, 0.2);
}

.debug-content {
  max-height: calc(80vh - 60px);
  overflow-y: auto;
  padding: 0;
}

.debug-section {
  margin: 0;
  padding: 1rem;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.debug-section:last-child {
  border-bottom: none;
}

.debug-section h4 {
  margin: 0 0 0.5rem 0;
  color: #00ff00;
  font-size: 0.9rem;
}

.debug-section pre {
  background: rgba(0, 0, 0, 0.3);
  padding: 0.5rem;
  border-radius: 4px;
  font-size: 0.7rem;
  overflow-x: auto;
  white-space: pre-wrap;
  word-wrap: break-word;
  margin: 0;
  color: #00ffff;
}

/* ============================================ */
/* RESPONSIVE DESIGN */
/* ============================================ */

@media (max-width: 768px) {
  .app {
    padding: 0.5rem;
  }

  .header h1 {
    font-size: 2rem;
  }

  .educational-panel {
    width: 95vw;
    left: 2.5vw;
  }

  .connection-status {
    flex-direction: column;
    gap: 0.75rem;
  }

  .tab-btn {
    padding: 0.6rem 1.5rem;
    font-size: 0.9rem;
  }

  .section-header {
    flex-direction: column;
    align-items: flex-start;
  }

  .section-header h2 {
    font-size: 1.5rem;
  }

  .main-price {
    font-size: 2.5rem;
  }

  .block-height {
    font-size: 2.5rem;
  }

  .block-main-info {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .block-stats-grid {
    grid-template-columns: 1fr;
  }

  .insights-dashboard {
    grid-template-columns: 1fr;
  }

  .price-stats, .block-info {
    grid-template-columns: 1fr;
  }

  .grid-container {
    grid-template-columns: 1fr;
  }

  .trade-row {
    grid-template-columns: auto 1fr auto;
    gap: 0.5rem;
  }

  .trade-row .time {
    display: none;
  }

  .mempool-stats {
    grid-template-columns: 1fr;
  }

  .tx-main-info {
    flex-direction: column;
    align-items: flex-start;
  }

  .tx-fee-info {
    grid-template-columns: 1fr;
    gap: 0.75rem;
  }

  .transactions-legend {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.75rem;
  }

  .debug-panel {
    width: 90vw;
    right: 5vw;
    top: 10px;
  }

  .debug-toggle {
    bottom: 10px;
    right: 10px;
  }
}

@media (max-width: 480px) {
  .header {
    padding: 1rem;
  }

  .header h1 {
    font-size: 1.8rem;
  }

  .educational-toggle {
    top: 10px;
    left: 10px;
  }

  .educational-btn {
    padding: 0.5rem 1rem;
    font-size: 0.8rem;
  }

  .tab-navigation {
    flex-direction: column;
    gap: 0.5rem;
  }

  .tab-btn {
    width: 100%;
  }

  .price-card, .card {
    padding: 1rem;
  }

  .main-price {
    font-size: 2rem;
  }

  .block-height {
    font-size: 2rem;
  }

  .action-buttons {
    flex-direction: column;
    width: 100%;
  }

  .btn-secondary, .btn-educational {
    width: 100%;
    text-align: center;
  }
}

/* ============================================ */
/* ANIMATIONS */
/* ============================================ */

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.7; }
}

.status-item.connected {
  animation: pulse 2s infinite;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.trade-row, .transaction-row, .order-row {
  animation: slideIn 0.3s ease-out;
}

/* ============================================ */
/* SCROLLBAR STYLING */
/* ============================================ */

.trades-list::-webkit-scrollbar,
.transactions-list::-webkit-scrollbar,
.educational-content::-webkit-scrollbar,
.debug-content::-webkit-scrollbar {
  width: 6px;
}

.trades-list::-webkit-scrollbar-track,
.transactions-list::-webkit-scrollbar-track,
.educational-content::-webkit-scrollbar-track,
.debug-content::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.05);
  border-radius: 3px;
}

.trades-list::-webkit-scrollbar-thumb,
.transactions-list::-webkit-scrollbar-thumb,
.educational-content::-webkit-scrollbar-thumb,
.debug-content::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.2);
  border-radius: 3px;
}

.trades-list::-webkit-scrollbar-thumb:hover,
.transactions-list::-webkit-scrollbar-thumb:hover,
.educational-content::-webkit-scrollbar-thumb:hover,
.debug-content::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 0, 0, 0.3);
}

/* ============================================ */
/* UTILITY CLASSES */
/* ============================================ */

.positive {
  color: #059669 !important;
}

.negative {
  color: #dc2626 !important;
}

.neutral {
  color: #6b7280 !important;
}

.text-center {
  text-align: center;
}

.font-mono {
  font-family: 'Courier New', monospace;
}

.font-bold {
  font-weight: 600;
}

.text-sm {
  font-size: 0.875rem;
}

/* ============================================ */
/* ESTILOS ADICIONALES PARA COMPONENTES MEJORADOS */
/* ============================================ */

/* Sub-tab navigation */
.sub-tab-navigation {
  display: flex;
  justify-content: center;
  gap: 0.5rem;
  margin-bottom: 1.5rem;
}

/* Prediction badges */
.prediction-badge {
  background: rgba(79, 70, 229, 0.1);
  color: #4f46e5;
  padding: 0.25rem 0.75rem;
  border-radius: 15px;
  font-size: 0.75rem;
  font-weight: 600;
}

/* Fee predictions timeline */
.fee-predictions {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.prediction-timeline {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem;
  background: rgba(0, 0, 0, 0.02);
  border-radius: 12px;
}

.prediction-item {
  text-align: center;
  flex: 1;
}

.prediction-time {
  font-size: 0.85rem;
  color: #6b7280;
  margin-bottom: 0.5rem;
  font-weight: 500;
}

.prediction-value {
  font-size: 1.25rem;
  font-weight: 700;
  color: #1f2937;
  margin-bottom: 0.25rem;
}

.prediction-value.current {
  color: #4f46e5;
  font-size: 1.5rem;
}

.prediction-desc {
  font-size: 0.8rem;
  color: #9ca3af;
}

.prediction-arrow {
  font-size: 1.5rem;
  color: #6b7280;
  margin: 0 1rem;
}

/* Fee analysis box */
.fee-analysis {
  background: rgba(245, 158, 11, 0.1);
  padding: 1rem;
  border-radius: 8px;
  margin-top: 1rem;
  border-left: 3px solid #f59e0b;
}

/* Sentiment analysis styles */
.sentiment-analysis {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.sentiment-gauge {
  text-align: center;
  margin-bottom: 1.5rem;
}

.sentiment-details h4 {
  margin-bottom: 1rem;
  color: #374151;
}

.sentiment-factor {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem;
  background: rgba(0, 0, 0, 0.02);
  border-radius: 8px;
  margin-bottom: 0.5rem;
}

/* Whale alerts styles */
.whale-alerts-list {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  max-height: 400px;
  overflow-y: auto;
}

.whale-alert {
  padding: 1rem;
  border-radius: 8px;
  margin-bottom: 0.75rem;
  transition: all 0.3s ease;
}

.whale-alert:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* Whale statistics */
.whale-stats {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.stat-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 1rem;
}

.whale-stat {
  text-align: center;
  padding: 1rem;
  background: rgba(79, 70, 229, 0.05);
  border-radius: 8px;
  border: 1px solid rgba(79, 70, 229, 0.1);
}

/* Whale interpretation */
.whale-interpretation {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.interpretation-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.interpretation-item {
  padding: 1rem;
  border-radius: 8px;
  line-height: 1.5;
}

/* Network dashboard styles */
.network-dashboard {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.hashrate-analysis, .difficulty-analysis {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.hashrate-main, .difficulty-main {
  text-align: center;
  margin-bottom: 1.5rem;
}

.hashrate-insights, .difficulty-insights {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.insight {
  padding: 1rem;
  border-radius: 8px;
  line-height: 1.6;
}

/* Mining economics */
.mining-economics {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.economics-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1.5rem;
}

.economics-item {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1.5rem;
  background: rgba(79, 70, 229, 0.05);
  border: 1px solid rgba(79, 70, 229, 0.1);
  border-radius: 12px;
  transition: all 0.3s ease;
}

.economics-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(79, 70, 229, 0.15);
  background: rgba(79, 70, 229, 0.08);
}

.economics-icon {
  font-size: 2rem;
  margin-top: 0.25rem;
}

.economics-content {
  flex: 1;
}

.economics-value {
  font-size: 1.5rem;
  font-weight: 700;
  color: #4f46e5;
  margin-bottom: 0.5rem;
}

.economics-label {
  font-weight: 600;
  color: #374151;
  font-size: 0.9rem;
  margin-bottom: 0.5rem;
  text-transform: uppercase;
  letter-spacing: 0.025em;
}

.economics-desc {
  font-size: 0.85rem;
  color: #6b7280;
  line-height: 1.4;
}

.mining-insights {
  margin-top: 2rem;
}

.mining-insights h4 {
  margin-bottom: 1rem;
  color: #374151;
}

.insights-grid {
  display: grid;
  gap: 1rem;
}

/* Enhanced responsive design */
@media (max-width: 768px) {
  .sub-tab-navigation {
    flex-wrap: wrap;
    justify-content: center;
  }

  .prediction-timeline {
    flex-direction: column;
    gap: 1rem;
  }

  .prediction-arrow {
    transform: rotate(90deg);
    margin: 0.5rem 0;
  }

  .economics-grid {
    grid-template-columns: 1fr;
  }

  .stat-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 480px) {
  .whale-alert {
    padding: 0.75rem;
  }

  .economics-item {
    padding: 1rem;
  }

  .economics-value {
    font-size: 1.25rem;
  }

  .stat-grid {
    grid-template-columns: 1fr;
  }
}

/* Additional utility classes */
.text-bullish {
  color: #059669 !important;
}

.text-bearish {
  color: #dc2626 !important;
}

.text-neutral {
  color: #6b7280 !important;
}

.bg-bullish {
  background: rgba(5, 150, 105, 0.1) !important;
  border-color: rgba(5, 150, 105, 0.3) !important;
}

.bg-bearish {
  background: rgba(220, 38, 38, 0.1) !important;
  border-color: rgba(220, 38, 38, 0.3) !important;
}

.bg-neutral {
  background: rgba(107, 114, 128, 0.1) !important;
  border-color: rgba(107, 114, 128, 0.3) !important;
}


/* ============================================ */
/* NUEVOS ESTILOS PARA GRÁFICOS */
/* ============================================ */

/* Contenedor principal de gráficos */
.chart-container {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(15px);
  border-radius: 16px;
  padding: 1.5rem;
  margin-bottom: 1.5rem;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
}

.chart-container:hover {
  transform: translateY(-2px);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
}

/* Header de gráficos */
.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
  flex-wrap: wrap;
  gap: 1rem;
}

.chart-header h4 {
  margin: 0;
  color: #374151;
  font-size: 1.1rem;
  font-weight: 600;
}

/* Leyenda de gráficos */
.chart-legend {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.85rem;
  color: #6b7280;
}

.legend-color {
  width: 12px;
  height: 12px;
  border-radius: 2px;
}

/* Contenedor del tab de gráficos */
.charts-tab {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.charts-header {
  margin-bottom: 1rem;
}

.charts-main-grid {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.charts-row {
  display: grid;
  gap: 1.5rem;
  animation: fadeInUp 0.6s ease-out;
}

.charts-row:nth-child(1) {
  grid-template-columns: 2fr 1fr;
}

.charts-row:nth-child(2) {
  grid-template-columns: 1fr;
  animation-delay: 0.1s;
}

.charts-row:nth-child(3) {
  grid-template-columns: 2fr 1fr;
  animation-delay: 0.2s;
}

.charts-row:nth-child(4) {
  grid-template-columns: 1fr;
  animation-delay: 0.3s;
}

.chart-col-full {
  grid-column: 1 / -1;
}

/* Estados de carga para gráficos */
.chart-loading {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 16px;
  padding: 3rem;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  height: 300px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.chart-loading:hover {
  transform: translateY(-2px);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
}

.loading-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
  text-align: center;
}

.loading-spinner {
  font-size: 3rem;
  animation: pulse 2s infinite;
}

.loading-explanation {
  font-size: 0.9rem;
  color: #6b7280;
  max-width: 300px;
  line-height: 1.4;
}

/* Estadísticas de gráficos */
.chart-stats-container {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 16px;
  padding: 1.5rem;
  margin-bottom: 1.5rem;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.stats-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.stats-header h4 {
  margin: 0;
  color: #374151;
  font-size: 1.1rem;
  font-weight: 600;
}

.stats-badge {
  background: rgba(79, 70, 229, 0.1);
  color: #4f46e5;
  padding: 0.25rem 0.75rem;
  border-radius: 15px;
  font-size: 0.75rem;
  font-weight: 600;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  background: rgba(0, 0, 0, 0.02);
  border-radius: 12px;
  border: 1px solid rgba(0, 0, 0, 0.05);
}

.stat-icon {
  font-size: 1.5rem;
}

.stat-content {
  flex: 1;
}

.stat-label {
  font-size: 0.85rem;
  color: #6b7280;
  margin-bottom: 0.25rem;
}

.stat-value {
  font-size: 1rem;
  font-weight: 600;
  color: #374151;
}

/* Panel de información de gráficos */
.charts-info-panel {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 16px;
  padding: 2rem;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  margin-top: 1rem;
}

.info-header {
  margin-bottom: 1.5rem;
}

.info-header h4 {
  margin: 0;
  color: #374151;
  font-size: 1.1rem;
  font-weight: 600;
}

.info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.info-item {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  padding: 1rem;
  background: rgba(79, 70, 229, 0.05);
  border-radius: 8px;
  border-left: 3px solid #4f46e5;
}

.info-item strong {
  color: #1f2937;
  font-weight: 600;
}

.info-item span {
  color: #6b7280;
  font-size: 0.9rem;
  line-height: 1.5;
}

.info-disclaimer {
  padding: 1rem;
  background: rgba(245, 158, 11, 0.1);
  border: 1px solid rgba(245, 158, 11, 0.3);
  border-radius: 8px;
  color: #92400e;
  font-size: 0.9rem;
  line-height: 1.5;
  text-align: center;
}

/* ============================================ */
/* MEJORAS PARA TABS EXISTENTES */
/* ============================================ */

/* Enhanced tab navigation */
.tab-content-wrapper {
  animation: fadeIn 0.4s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.tab-indicator {
  margin: 1rem 0 2rem 0;
  text-align: center;
}

.tab-description {
  display: inline-flex;
  align-items: center;
  padding: 0.75rem 1.5rem;
  background: rgba(79, 70, 229, 0.1);
  color: #4f46e5;
  border-radius: 20px;
  font-size: 0.9rem;
  font-weight: 500;
  border: 1px solid rgba(79, 70, 229, 0.2);
}

/* Connection info */
.connection-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 2rem;
  padding: 1rem;
  background: rgba(0, 0, 0, 0.02);
  border-radius: 12px;
  border: 1px solid rgba(0, 0, 0, 0.05);
  flex-wrap: wrap;
  gap: 1rem;
}

.connection-badges {
  display: flex;
  gap: 0.75rem;
  flex-wrap: wrap;
}

.badge {
  padding: 0.25rem 0.75rem;
  border-radius: 15px;
  font-size: 0.8rem;
  font-weight: 600;
  border: 1px solid;
  transition: all 0.3s ease;
}

.badge.connected {
  background: rgba(16, 185, 129, 0.1);
  color: #059669;
  border-color: rgba(16, 185, 129, 0.3);
}

.badge.disconnected {
  background: rgba(107, 114, 128, 0.1);
  color: #6b7280;
  border-color: rgba(107, 114, 128, 0.3);
}

.data-freshness {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.9rem;
}

.freshness-label {
  color: #6b7280;
}

.freshness-time {
  color: #374151;
  font-weight: 600;
  font-family: 'Courier New', monospace;
}

/* Enhanced sub-tab navigation */
.sub-tab-navigation .tab-btn {
  position: relative;
  transition: all 0.3s ease;
}

.sub-tab-navigation .tab-btn:hover:not(.active) {
  background: rgba(79, 70, 229, 0.1);
  color: #4f46e5;
  transform: translateY(-1px);
}

.sub-tab-navigation .tab-btn.active {
  background: linear-gradient(45deg, #4f46e5, #7c3aed);
  color: white;
  box-shadow: 0 4px 15px rgba(79, 70, 229, 0.3);
  transform: translateY(-1px);
}

.sub-tab-navigation .tab-btn.active::after {
  content: '';
  position: absolute;
  bottom: -8px;
  left: 50%;
  transform: translateX(-50%);
  width: 0;
  height: 0;
  border-left: 6px solid transparent;
  border-right: 6px solid transparent;
  border-bottom: 6px solid rgba(79, 70, 229, 0.1);
}

/* ============================================ */
/* ANIMACIONES ADICIONALES */
/* ============================================ */

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.tab-content-wrapper > * {
  animation: slideInUp 0.5s ease-out;
}

/* Loading animations */
.tab-loading {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 300px;
  flex-direction: column;
  gap: 1rem;
}

.tab-loading-spinner {
  font-size: 2rem;
  animation: spin 2s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.tab-loading-text {
  color: #6b7280;
  font-style: italic;
}

/* Pulse animation para elementos en vivo */
@keyframes pulse {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.7;
    transform: scale(1.05);
  }
}

/* ============================================ */
/* RESPONSIVE DESIGN PARA GRÁFICOS */
/* ============================================ */

@media (max-width: 1200px) {
  .charts-row:nth-child(odd) {
    grid-template-columns: 1fr;
  }

  .info-grid {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 768px) {
  /* Charts responsive */
  .charts-tab {
    gap: 1rem;
  }

  .charts-main-grid {
    gap: 1rem;
  }

  .chart-loading {
    height: 200px;
    padding: 2rem;
  }

  .loading-content {
    gap: 0.75rem;
  }

  .loading-spinner {
    font-size: 2rem;
  }

  .charts-info-panel {
    padding: 1.5rem;
  }

  .info-grid {
    gap: 1rem;
  }

  /* Chart headers */
  .chart-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }

  .chart-legend {
    justify-content: flex-start;
  }

  /* Stats responsive */
  .stats-grid {
    grid-template-columns: 1fr;
  }

  /* Connection info responsive */
  .connection-info {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }

  .connection-badges {
    width: 100%;
    justify-content: center;
  }

  .data-freshness {
    align-self: center;
  }

  .tab-indicator {
    margin: 0.75rem 0 1.5rem 0;
  }

  .tab-description {
    padding: 0.5rem 1rem;
    font-size: 0.85rem;
    text-align: center;
  }
}

@media (max-width: 480px) {
  /* Charts mobile */
  .charts-header .section-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }

  .action-buttons {
    flex-direction: column;
    width: 100%;
  }

  .btn-secondary {
    width: 100%;
    text-align: center;
  }

  .chart-loading {
    height: 150px;
    padding: 1rem;
  }

  .loading-explanation {
    font-size: 0.8rem;
  }

  /* Sub-tab navigation mobile */
  .sub-tab-navigation {
    grid-template-columns: 1fr 1fr;
    display: grid;
    gap: 0.5rem;
  }

  .sub-tab-navigation .tab-btn {
    padding: 0.75rem 0.5rem;
    font-size: 0.8rem;
    text-align: center;
  }

  /* Connection badges mobile */
  .connection-badges {
    grid-template-columns: repeat(2, 1fr);
    display: grid;
    gap: 0.5rem;
    width: 100%;
  }

  .badge {
    text-align: center;
  }
}

/* ============================================ */
/* ESTILOS PARA TOOLTIPS PERSONALIZADOS */
/* ============================================ */

.custom-tooltip {
  background: rgba(255, 255, 255, 0.98) !important;
  border: 1px solid #e5e7eb !important;
  border-radius: 8px !important;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1) !important;
  padding: 8px 12px !important;
}

.custom-tooltip .tooltip-label {
  font-weight: 600;
  color: #374151;
  margin-bottom: 4px;
}

.custom-tooltip .tooltip-content {
  color: #6b7280;
  font-size: 14px;
}

/* ============================================ */
/* ESTILOS PARA INDICADORES ESPECIALES */
/* ============================================ */

.trend-indicator {
  display: inline-flex;
  align-items: center;
  gap: 0.25rem;
}

.trend-up {
  color: #10b981;
}

.trend-down {
  color: #ef4444;
}

.trend-neutral {
  color: #6b7280;
}

.metric-badge {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  background: rgba(79, 70, 229, 0.1);
  color: #4f46e5;
  border-radius: 20px;
  font-size: 0.9rem;
  font-weight: 600;
  border: 1px solid rgba(79, 70, 229, 0.2);
}

.metric-badge.bullish {
  background: rgba(16, 185, 129, 0.1);
  color: #059669;
  border-color: rgba(16, 185, 129, 0.3);
}

.metric-badge.bearish {
  background: rgba(239, 68, 68, 0.1);
  color: #dc2626;
  border-color: rgba(239, 68, 68, 0.3);
}

.metric-badge.neutral {
  background: rgba(245, 158, 11, 0.1);
  color: #d97706;
  border-color: rgba(245, 158, 11, 0.3);
}

/* ============================================ */
/* UTILIDADES ADICIONALES */
/* ============================================ */

.text-gradient {
  background: linear-gradient(45deg, #4f46e5, #7c3aed);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.glass-effect {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.shadow-glow {
  box-shadow: 0 0 20px rgba(79, 70, 229, 0.3);
}

.animate-bounce-subtle {
  animation: bounceSubtle 2s infinite;
}

@keyframes bounceSubtle {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-3px);
  }
  60% {
    transform: translateY(-2px);
  }
}

/* ============================================ */
/* THEME VARIABLES (para futuras mejoras) */
/* ============================================ */

:root {
  --chart-primary: #4f46e5;
  --chart-secondary: #7c3aed;
  --chart-success: #10b981;
  --chart-warning: #f59e0b;
  --chart-danger: #ef4444;
  --chart-info: #3b82f6;
  --chart-neutral: #6b7280;

  --chart-bg-primary: rgba(79, 70, 229, 0.1);
  --chart-bg-secondary: rgba(124, 58, 237, 0.1);
  --chart-bg-success: rgba(16, 185, 129, 0.1);
  --chart-bg-warning: rgba(245, 158, 11, 0.1);
  --chart-bg-danger: rgba(239, 68, 68, 0.1);
  --chart-bg-info: rgba(59, 130, 246, 0.1);
  --chart-bg-neutral: rgba(107, 114, 128, 0.1);

  --chart-border-radius: 16px;
  --chart-border-radius-small: 8px;
  --chart-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  --chart-shadow-hover: 0 12px 40px rgba(0, 0, 0, 0.15);
}

/* ============================================ */
/* DARK MODE PREPARATION (para futuro) */
/* ============================================ */

@media (prefers-color-scheme: dark) {
  /* Aquí se pueden agregar estilos para modo oscuro */
  .chart-container {
    background: rgba(31, 41, 55, 0.95);
    border-color: rgba(75, 85, 99, 0.2);
  }

  .chart-loading {
    background: rgba(31, 41, 55, 0.95);
    border-color: rgba(75, 85, 99, 0.2);
  }

  .loading-explanation {
    color: #9ca3af;
  }
}
