const express = require('express');
const http = require('http');
const socketIo = require('socket.io');
const cors = require('cors');

// Importar servicios
const BlockchainService = require('./src/services/blockchainService');

// ============================================
// CONFIGURACIÓN DEL SERVIDOR
// ============================================

const app = express();
const server = http.createServer(app);

const io = socketIo(server, {
  cors: {
    origin: "http://localhost:5173",
    methods: ["GET", "POST"],
    credentials: true
  }
});

app.use(cors());
app.use(express.json());

// ============================================
// INICIALIZACIÓN DE SERVICIOS
// ============================================

const blockchainService = new BlockchainService();

let connectedClients = 0;
let serverStats = {
  startTime: Date.now(),
  blockchainUpdates: 0,
  totalMessages: 0
};



// ============================================
// CONFIGURACIÓN DE CALLBACKS PARA BLOCKCHAIN
// ============================================

blockchainService.setCallbacks({
  onBlockUpdate: (blockData, isNewBlock) => {
    serverStats.blockchainUpdates++;
    serverStats.totalMessages++;

    if (isNewBlock) {
      // Nuevo bloque minado - evento especial
      io.emit('new-block', blockData);
      io.emit('new-block-realtime', blockData);
      console.log(`⛏️ NEW BITCOIN BLOCK MINED! #${blockData.height} - ${blockData.txCount} transactions`);
    }

    // Emitir actualización general de blockchain
    io.emit('blockchain-block', blockData);
  },

  onMempoolUpdate: (mempoolData) => {
    io.emit('blockchain-mempool', mempoolData);
    console.log(`📊 Mempool: ${mempoolData.count || 'N/A'} pending transactions`);
  },

  onNewTransaction: (txData) => {
    io.emit('blockchain-transaction', txData);
    // No log individual de transacciones para evitar spam
  },

  onConnectionChange: (status) => {
    io.emit('blockchain-connection', status);
    console.log(`🔗 Blockchain Connection: ${status.connected ? 'CONNECTED' : 'DISCONNECTED'}`);
  },

  onPredictionUpdate: (predictions) => {
    io.emit('blockchain-predictions', predictions);
    console.log(`🔮 Prediction Update: ${predictions.interpretation} (${Math.round(predictions.confidence)}% confidence)`);
  },

  onWhaleAlert: (alert) => {
    io.emit('whale-alert', alert);
    console.log(`🐋 WHALE ALERT: ${alert.message}`);
  }
});

// ============================================
// CICLOS DE ACTUALIZACIÓN
// ============================================

// Actualizar datos de Blockchain cada 30 segundos
setInterval(async () => {
  try {
    const blockchainData = await blockchainService.updateAllData();
    // Emitir datos completos de blockchain
    io.emit('blockchain-data', blockchainData);
  } catch (error) {
    console.error('❌ Error in Blockchain update cycle:', error.message);
  }
}, 30000);

// Stats del servidor cada 60 segundos
setInterval(() => {
  const uptime = Date.now() - serverStats.startTime;
  console.log(`📈 Server Stats: ${connectedClients} clients | ${serverStats.blockchainUpdates} Blockchain updates | Uptime: ${Math.floor(uptime / 60000)}m`);
}, 60000);

// ============================================
// INICIALIZACIÓN DE SERVICIOS
// ============================================

async function initializeServices() {
  console.log('🚀 Initializing services...');

  try {
    // Inicializar datos iniciales
    console.log('📊 Fetching initial data...');
    await blockchainService.updateAllData();

    // Conectar WebSockets
    console.log('🔌 Connecting WebSockets...');
    blockchainService.connectWebSocket();

    console.log('✅ All services initialized successfully!');
  } catch (error) {
    console.error('❌ Error initializing services:', error);
  }
}

// Ejecutar inicialización
initializeServices();

// ============================================
// SOCKET.IO CONNECTION HANDLING
// ============================================

io.on('connection', (socket) => {
  connectedClients++;
  console.log(`👤 Client connected. Total: ${connectedClients}`);

  // Enviar datos actuales inmediatamente
  const currentData = {
    blockchain: blockchainService.getAllData(),
    server: {
      ...serverStats,
      uptime: Date.now() - serverStats.startTime,
      connectedClients
    }
  };

  // Enviar datos completos
  socket.emit('initial-data', currentData);

  if (currentData.blockchain) {
    socket.emit('blockchain-data', currentData.blockchain);
  }

  // Manejar solicitudes específicas del cliente

  socket.on('request-blockchain-refresh', async () => {
    try {
      const data = await blockchainService.updateAllData();
      socket.emit('blockchain-data', data);
    } catch (error) {
      socket.emit('error', { type: 'blockchain-refresh', message: error.message });
    }
  });

  socket.on('request-predictions', async () => {
    try {
      const predictions = blockchainService.getPredictions();
      socket.emit('blockchain-predictions', predictions);
    } catch (error) {
      socket.emit('error', { type: 'predictions', message: error.message });
    }
  });

  socket.on('request-whale-activity', async () => {
    try {
      const whaleActivity = blockchainService.getWhaleActivity();
      socket.emit('whale-activity', whaleActivity);
    } catch (error) {
      socket.emit('error', { type: 'whale-activity', message: error.message });
    }
  });

  socket.on('request-network-metrics', async () => {
    try {
      const networkMetrics = blockchainService.getNetworkMetrics();
      socket.emit('network-metrics', networkMetrics);
    } catch (error) {
      socket.emit('error', { type: 'network-metrics', message: error.message });
    }
  });

  // Cliente desconectado
  socket.on('disconnect', (reason) => {
    connectedClients--;
    console.log(`👤 Client disconnected (${reason}). Total: ${connectedClients}`);
  });
});

// ============================================
// REST API ENDPOINTS
// ============================================

// Health check general
app.get('/api/health', (req, res) => {
  res.json({
    status: 'OK',
    uptime: Date.now() - serverStats.startTime,
    connected_clients: connectedClients,
    services: {
      blockchain: blockchainService.getConnectionStatus()
    },
    stats: serverStats
  });
});



// Endpoints específicos de Blockchain
app.get('/api/blockchain', (req, res) => {
  res.json(blockchainService.getAllData());
});

app.get('/api/blockchain/block', (req, res) => {
  const blockData = blockchainService.getCurrentBlock();
  if (blockData) {
    res.json(blockData);
  } else {
    res.status(503).json({ error: 'Block data not available' });
  }
});

app.get('/api/blockchain/mempool', (req, res) => {
  const mempoolData = blockchainService.getMempoolInfo();
  res.json(mempoolData);
});

app.get('/api/blockchain/transactions', (req, res) => {
  const transactions = blockchainService.getRecentTransactions();
  res.json(transactions);
});

// Endpoint para predicciones
app.get('/api/blockchain/predictions', (req, res) => {
  const predictions = blockchainService.getPredictions();
  res.json(predictions || { error: 'Predictions not available yet' });
});

// Endpoint para actividad de ballenas
app.get('/api/blockchain/whales', (req, res) => {
  const whaleActivity = blockchainService.getWhaleActivity();
  res.json(whaleActivity || { error: 'Whale data not available yet' });
});

// Endpoint para métricas de red
app.get('/api/blockchain/network', (req, res) => {
  const networkMetrics = blockchainService.getNetworkMetrics();
  res.json(networkMetrics || { error: 'Network metrics not available yet' });
});

// Endpoint combinado con todas las predicciones
app.get('/api/blockchain/analytics', (req, res) => {
  const data = blockchainService.getAllData();
  res.json({
    predictions: data.predictions || {},
    whaleActivity: data.whaleActivity || {},
    networkMetrics: data.networkMetrics || {},
    sentiment: data.predictions?.sentiment || {},
    timestamp: Date.now()
  });
});

// Endpoint combinado (compatibilidad)
app.get('/api/all', (req, res) => {
  res.json({
    blockchain: blockchainService.getAllData(),
    server: {
      ...serverStats,
      uptime: Date.now() - serverStats.startTime,
      connectedClients
    }
  });
});

// ============================================
// SERVER STARTUP
// ============================================

const PORT = process.env.PORT || 3001;

server.listen(PORT, () => {
  console.log(`🚀 Bitcoin Trading Server running on port ${PORT}`);
  console.log(`🔗 WebSocket endpoint: ws://localhost:${PORT}`);
  console.log(`🌐 API endpoint: http://localhost:${PORT}/api`);
  console.log(`⛓️ Blockchain WebSocket: Enabled`);
  console.log(`⚡ Real-time data updates: Active`);
});

// ============================================
// GRACEFUL SHUTDOWN
// ============================================

function gracefulShutdown(signal) {
  console.log(`🔴 Received ${signal}. Shutting down gracefully...`);

  // Desconectar servicios
  blockchainService.disconnect();

  // Cerrar servidor
  server.close(() => {
    console.log('✅ Server closed successfully');
    process.exit(0);
  });

  // Forzar cierre después de 10 segundos
  setTimeout(() => {
    console.log('⚠️ Forcing shutdown...');
    process.exit(1);
  }, 10000);
}

process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));
process.on('SIGINT', () => gracefulShutdown('SIGINT'));

// Manejar errores no capturados
process.on('unhandledRejection', (reason, promise) => {
  console.error('❌ Unhandled Rejection at:', promise, 'reason:', reason);
});

process.on('uncaughtException', (error) => {
  console.error('❌ Uncaught Exception:', error);
  gracefulShutdown('UNCAUGHT_EXCEPTION');
});
